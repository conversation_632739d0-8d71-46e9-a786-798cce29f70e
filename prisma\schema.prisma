// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User model (for future user management)
model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  assessments Assessment[]

  @@map("users")
}

// Assessment model to store RIASEC and OCEAN scores
model Assessment {
  id        String   @id @default(cuid())
  userId    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // RIASEC Scores (0-30 each)
  riasecR Int
  riasecI Int
  riasecA Int
  riasecS Int
  riasecE Int
  riasecC Int

  // OCEAN Scores (5-25 each)
  oceanO Int
  oceanC Int
  oceanE Int
  oceanA Int
  oceanN Int

  // Relations
  user    User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  profile Profile?

  @@map("assessments")
}

// Profile model to store generated AI analysis
model Profile {
  id           String   @id @default(cuid())
  assessmentId String   @unique
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // AI Generated Profile Data
  profileTitle       String
  profileDescription String
  strengths          String // JSON array as string
  careerSuggestions  String // JSON array as string
  workStyle          String
  developmentAreas   String // JSON array as string
  personalityInsights String // JSON array as string
  careerFit          String

  // Metadata
  generatedAt DateTime @default(now())
  aiModel     String   @default("gemini-2.5-pro")

  // Relations
  assessment Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)

  @@map("profiles")
}
