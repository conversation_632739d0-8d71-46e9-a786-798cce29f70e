import { GoogleGenAI, Type } from "@google/genai";
import { RiasecScores, OceanScores } from '../types';

// Interface untuk response gabungan RIASEC + OCEAN
export interface CombinedProfileResponse {
  profileTitle: string;
  profileDescription: string;
  strengths: string[];
  careerSuggestions: string[];
  workStyle: string;
  developmentAreas: string[];
  personalityInsights: string[];
  careerFit: string;
}

// Interface untuk cache entry
interface CacheEntry {
  data: CombinedProfileResponse;
  timestamp: number;
}

// Interface untuk rate limiting
interface RateLimitInfo {
  requestCount: number;
  resetTime: number;
}

// Function to create combined prompt
function createCombinedPrompt(riasecScores: RiasecScores, oceanScores: OceanScores): string {
  // Konversi skor RIASEC dari skala 0-30 ke skala 1-100
  const convertedRiasec = {
    R: Math.round((riasecScores.R / 30) * 100),
    I: Math.round((riasecScores.I / 30) * 100),
    A: Math.round((riasecScores.A / 30) * 100),
    S: Math.round((riasecScores.S / 30) * 100),
    E: Math.round((riasecScores.E / 30) * 100),
    C: Math.round((riasecScores.C / 30) * 100)
  };

  // Konversi skor OCEAN dari skala 5-25 ke skala 1-100
  const convertedOcean = {
    O: Math.round((oceanScores.O / 25) * 100),
    C: Math.round((oceanScores.C / 25) * 100),
    E: Math.round((oceanScores.E / 25) * 100),
    A: Math.round((oceanScores.A / 25) * 100),
    N: Math.round((oceanScores.N / 25) * 100)
  };

  return `# ROLE
You are an expert-level Career Analytics Engine. Your specialization is synthesizing RIASEC (Holland Codes) and Big Five (OCEAN) personality data into a cohesive, objective, and data-driven career profile.

# CONTEXT
You are operating as the core logic for a web application providing career guidance to high school students. The analysis must be objective, analytical, and framed like a clinical report. It should focus on strengths and identify challenges as areas to de-emphasize, not as personal failings. The final output must be a single, valid JSON object and nothing else.

# INPUT DATA
## RIASEC Scores (Holland Codes) - Scale 1-100:
- Realistic (R): ${convertedRiasec.R}
- Investigative (I): ${convertedRiasec.I}
- Artistic (A): ${convertedRiasec.A}
- Social (S): ${convertedRiasec.S}
- Enterprising (E): ${convertedRiasec.E}
- Conventional (C): ${convertedRiasec.C}

## OCEAN Scores (Big Five Personality) - Scale 1-100:
- Openness (O): ${convertedOcean.O}
- Conscientiousness (C): ${convertedOcean.C}
- Extraversion (E): ${convertedOcean.E}
- Agreeableness (A): ${convertedOcean.A}
- Neuroticism (N): ${convertedOcean.N}

# TASK
Your sole task is to analyze the provided RIASEC and OCEAN scores and generate a single JSON object that conforms to the schema provided in the "OUTPUT FORMAT" section. You will accomplish this by following the "THOUGHT PROCESS" outlined below.

# THOUGHT PROCESS (Internal Monologue - Do NOT show in output)
You will follow this 4-step reasoning process internally before constructing the final JSON output.

### Step 1: DECONSTRUCT
- Identify the top 2-3 highest-scoring RIASEC codes. These are the primary career interest drivers.
- Identify the most significant OCEAN scores (both high and low, e.g., >70 or <30). These traits are the "how" and "why" behind the user's behavior.
- Briefly note the core definition of each identified code and trait.

### Step 2: SYNTHESIZE
- Look for synergies between the dominant RIASEC codes and significant OCEAN traits.
- Identify potential tensions or contradictions between interests and personality.
- Consider how the personality traits would manifest in the preferred work environments.

### Step 3: DEVELOP
- Based on the synthesis in Step 2, generate the core content for each field in the JSON schema.
- Brainstorm 5 strengths that emerge from the strongest synergies.
- Brainstorm 5 career suggestions that are a strong match for BOTH the interests (RIASEC) and the personality (OCEAN).
- Define the ideal work style.
- Formulate the development areas from the identified tensions.
- Extract specific personality insights from the unique combination.
- Write a concise "careerFit" explanation that ties the core RIASEC/OCEAN combo to the suggested careers.

### Step 4: CONSTRUCT
- Format all content into the required JSON structure.
- Ensure all text is concise, professional, and actionable.
- Double-check that the output is valid JSON with no additional text.

# OUTPUT FORMAT
Return ONLY a valid JSON object with this exact structure (no additional text, explanations, or formatting):`;
}

// Schema untuk response gabungan RIASEC + OCEAN
const combinedProfileResponseSchema = {
  type: Type.OBJECT,
  properties: {
    profileTitle: {
      type: Type.STRING,
      description: "Judul profil yang menggabungkan insights RIASEC dan OCEAN (Dengan Bahasa inggris yang singkat)"
    },
    profileDescription: {
      type: Type.STRING,
      description: "Deskripsi kepribadian dalam DUA kalimat yang mengintegrasikan kedua model 2 kalimat singkat"
    },
    strengths: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 kekuatan utama berdasarkan kombinasi RIASEC dan OCEAN dalam SATU kalimat singkat"
    },
    careerSuggestions: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "5 rekomendasi karier yang mempertimbangkan minat dan kepribadian (use english)"
    },
    workStyle: {
      type: Type.STRING,
      description: "Gaya kerja ideal berdasarkan profil kepribadian dalam 2-3 kalimat"
    },
    developmentAreas: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "3-4 area pengembangan berdasarkan analisis profil dalam SATU kalimat singkat"
    },
    personalityInsights: {
      type: Type.ARRAY,
      items: {
        type: Type.STRING
      },
      description: "3-4 insight kepribadian unik dari kombinasi RIASEC dan OCEAN dalam SATU kalimat singkat"
    },
    careerFit: {
      type: Type.STRING,
      description: "Penjelasan mengapa karier yang disarankan cocok dengan profil dalam 2-3 kalimat"
    }
  },
  propertyOrdering: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle", "developmentAreas", "personalityInsights", "careerFit"],
  required: ["profileTitle", "profileDescription", "strengths", "careerSuggestions", "workStyle", "developmentAreas", "personalityInsights", "careerFit"]
};

// Server-side Gemini service class with singleton pattern
export class ServerGeminiProfileService {
  private static instance: ServerGeminiProfileService;
  private ai: GoogleGenAI;
  private cache: Map<string, CacheEntry> = new Map();
  private rateLimitInfo: RateLimitInfo = { requestCount: 0, resetTime: 0 };
  private readonly CACHE_TTL = 60 * 60 * 1000; // 1 hour cache
  private readonly MAX_REQUESTS_PER_MINUTE = 4; // Conservative limit (below 5)
  private readonly RETRY_DELAYS = [1000, 2000, 5000, 10000]; // Exponential backoff delays

  private constructor() {
    const apiKey = process.env.GEMINI_API_KEY;

    if (!apiKey) {
      throw new Error('GEMINI_API_KEY is not set in environment variables');
    }

    this.ai = new GoogleGenAI({
      apiKey: apiKey
    });

    // Set up periodic cache cleanup (every 30 minutes)
    setInterval(() => {
      this.cleanupCache();
    }, 30 * 60 * 1000);
  }

  // Singleton getInstance method
  public static getInstance(): ServerGeminiProfileService {
    if (!ServerGeminiProfileService.instance) {
      ServerGeminiProfileService.instance = new ServerGeminiProfileService();
    }
    return ServerGeminiProfileService.instance;
  }

  // Generate cache key from scores
  private generateCacheKey(riasecScores: RiasecScores, oceanScores: OceanScores): string {
    const riasecKey = `R${riasecScores.R}I${riasecScores.I}A${riasecScores.A}S${riasecScores.S}E${riasecScores.E}C${riasecScores.C}`;
    const oceanKey = `O${oceanScores.O}C${oceanScores.C}E${oceanScores.E}A${oceanScores.A}N${oceanScores.N}`;
    return `${riasecKey}_${oceanKey}`;
  }

  // Check if we can make a request (rate limiting)
  private canMakeRequest(): boolean {
    const now = Date.now();

    // Reset counter if minute has passed
    if (now >= this.rateLimitInfo.resetTime) {
      this.rateLimitInfo.requestCount = 0;
      this.rateLimitInfo.resetTime = now + 60000; // Next minute
    }

    return this.rateLimitInfo.requestCount < this.MAX_REQUESTS_PER_MINUTE;
  }

  // Increment request counter
  private incrementRequestCount(): void {
    this.rateLimitInfo.requestCount++;
  }

  // Sleep function for delays
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Method untuk analisis gabungan RIASEC + OCEAN dengan caching dan retry logic
  async generateCombinedProfile(riasecScores: RiasecScores, oceanScores: OceanScores): Promise<CombinedProfileResponse> {
    const cacheKey = this.generateCacheKey(riasecScores, oceanScores);

    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
      console.log('Returning cached profile for key:', cacheKey);
      return cached.data;
    }

    // Try to generate with retry logic
    for (let attempt = 0; attempt < this.RETRY_DELAYS.length + 1; attempt++) {
      try {
        // Check rate limit before making request
        if (!this.canMakeRequest()) {
          const waitTime = this.rateLimitInfo.resetTime - Date.now();
          console.log(`Rate limit reached, waiting ${waitTime}ms before retry`);
          await this.sleep(Math.max(waitTime, 1000));
          continue;
        }

        const prompt = createCombinedPrompt(riasecScores, oceanScores);

        // Increment request counter
        this.incrementRequestCount();

        // Menggunakan structured output dengan schema gabungan
        const response = await this.ai.models.generateContent({
          model: "gemini-2.5-pro",
          contents: prompt,
          config: {
            responseMimeType: "application/json",
            responseSchema: combinedProfileResponseSchema,
            temperature: 0.7,
            topP: 0.8,
            topK: 40,
          }
        });

        const responseText = response.text;
        if (!responseText) {
          throw new Error('Empty response from Gemini AI');
        }

        const profileData: CombinedProfileResponse = JSON.parse(responseText);

        // Validasi response
        if (!profileData.profileTitle || !profileData.profileDescription ||
            !profileData.strengths || !profileData.careerSuggestions ||
            !profileData.workStyle || !profileData.developmentAreas ||
            !profileData.personalityInsights || !profileData.careerFit) {
          throw new Error('Invalid response structure from Gemini AI');
        }

        // Cache successful response
        this.cache.set(cacheKey, {
          data: profileData,
          timestamp: Date.now()
        });

        console.log('Successfully generated and cached profile for key:', cacheKey);
        return profileData;

      } catch (error: any) {
        console.error(`Attempt ${attempt + 1} failed:`, error);

        // Check if it's a rate limit error (multiple ways to detect)
        const isRateLimitError =
          error?.status === 429 ||
          error?.message?.includes('quota') ||
          error?.message?.includes('rate limit') ||
          error?.message?.includes('RESOURCE_EXHAUSTED') ||
          (error?.error && error.error.code === 429) ||
          (error?.error && error.error.status === 'RESOURCE_EXHAUSTED');

        if (isRateLimitError) {
          if (attempt < this.RETRY_DELAYS.length) {
            const delay = this.RETRY_DELAYS[attempt];
            console.log(`Rate limit detected, retrying in ${delay}ms (attempt ${attempt + 1}/${this.RETRY_DELAYS.length + 1})`);
            await this.sleep(delay);
            continue;
          } else {
            console.log('Rate limit error: All retry attempts exhausted');
            break;
          }
        }

        // For other errors, break immediately unless it's the first attempt
        if (attempt === 0 && !isRateLimitError) {
          console.log('Non-rate-limit error on first attempt, breaking immediately');
          break;
        }
      }
    }

    console.log('All attempts failed, returning fallback profile');
    // Fallback ke response default jika semua attempt gagal
    return this.getCombinedFallbackProfile();
  }

  // Clean up expired cache entries
  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.CACHE_TTL) {
        this.cache.delete(key);
      }
    }
  }

  // Public method to clear cache if needed
  public clearCache(): void {
    this.cache.clear();
    console.log('Cache cleared');
  }

  // Get cache and rate limit statistics
  public getStats(): { cacheSize: number; rateLimitInfo: RateLimitInfo; cacheEntries: string[] } {
    return {
      cacheSize: this.cache.size,
      rateLimitInfo: { ...this.rateLimitInfo },
      cacheEntries: Array.from(this.cache.keys())
    };
  }

  // Fallback profile untuk analisis gabungan
  private getCombinedFallbackProfile(): CombinedProfileResponse {
    return {
      profileTitle: "Balanced Professional Profile",
      profileDescription: "Profil yang menunjukkan keseimbangan antara minat praktis dan analitis dengan kepribadian yang adaptif. Kombinasi ini menciptakan potensi untuk berkembang di berbagai bidang karier.",
      strengths: [
        "Kemampuan beradaptasi dengan berbagai situasi kerja",
        "Keseimbangan antara pemikiran logis dan kreativitas",
        "Keterampilan komunikasi yang baik dalam tim",
        "Pendekatan sistematis dalam menyelesaikan masalah",
        "Fleksibilitas dalam menghadapi perubahan"
      ],
      careerSuggestions: [
        "Project Manager",
        "Business Analyst",
        "Technical Consultant",
        "Operations Manager",
        "Product Coordinator"
      ],
      workStyle: "Bekerja dengan baik dalam lingkungan yang terstruktur namun fleksibel. Menyukai kolaborasi tim dengan tanggung jawab individual yang jelas.",
      developmentAreas: [
        "Mengembangkan spesialisasi dalam bidang tertentu",
        "Meningkatkan kepercayaan diri dalam pengambilan keputusan",
        "Memperkuat keterampilan kepemimpinan",
        "Mengasah kemampuan presentasi dan public speaking"
      ],
      personalityInsights: [
        "Memiliki keseimbangan yang baik antara introvert dan ekstrovert",
        "Cenderung mengutamakan harmoni dalam hubungan kerja",
        "Menunjukkan konsistensi dalam kinerja dan komitmen",
        "Memiliki potensi untuk berkembang dengan bimbingan yang tepat"
      ],
      careerFit: "Karier yang disarankan cocok karena memungkinkan penggunaan berbagai keterampilan dan minat. Profil ini menunjukkan kemampuan untuk beradaptasi dengan berbagai peran dan tanggung jawab."
    };
  }
}
