import { PrismaClient } from '../generated/prisma';
import { RiasecScores, OceanScores } from '../types';

// Global Prisma instance to prevent multiple connections in development
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Database service class
export class DatabaseService {
  // Create new assessment
  async createAssessment(
    riasecScores: RiasecScores,
    oceanScores: OceanScores,
    userId?: string
  ) {
    return await prisma.assessment.create({
      data: {
        userId,
        riasecR: riasecScores.R,
        riasecI: riasecScores.I,
        riasecA: riasecScores.A,
        riasecS: riasecScores.S,
        riasecE: riasecScores.E,
        riasecC: riasecScores.C,
        oceanO: oceanScores.O,
        oceanC: oceanScores.C,
        oceanE: oceanScores.E,
        oceanA: oceanScores.A,
        oceanN: oceanScores.N,
      },
    });
  }

  // Get assessment by ID
  async getAssessment(assessmentId: string) {
    return await prisma.assessment.findUnique({
      where: { id: assessmentId },
      include: { profile: true },
    });
  }

  // Get assessment with converted scores
  async getAssessmentWithScores(assessmentId: string) {
    const assessment = await this.getAssessment(assessmentId);
    if (!assessment) return null;

    return {
      ...assessment,
      riasecScores: {
        R: assessment.riasecR,
        I: assessment.riasecI,
        A: assessment.riasecA,
        S: assessment.riasecS,
        E: assessment.riasecE,
        C: assessment.riasecC,
      } as RiasecScores,
      oceanScores: {
        O: assessment.oceanO,
        C: assessment.oceanC,
        E: assessment.oceanE,
        A: assessment.oceanA,
        N: assessment.oceanN,
      } as OceanScores,
    };
  }

  // Create profile for assessment
  async createProfile(
    assessmentId: string,
    profileData: {
      profileTitle: string;
      profileDescription: string;
      strengths: string[];
      careerSuggestions: string[];
      workStyle: string;
      developmentAreas: string[];
      personalityInsights: string[];
      careerFit: string;
    }
  ) {
    return await prisma.profile.create({
      data: {
        assessmentId,
        profileTitle: profileData.profileTitle,
        profileDescription: profileData.profileDescription,
        strengths: JSON.stringify(profileData.strengths),
        careerSuggestions: JSON.stringify(profileData.careerSuggestions),
        workStyle: profileData.workStyle,
        developmentAreas: JSON.stringify(profileData.developmentAreas),
        personalityInsights: JSON.stringify(profileData.personalityInsights),
        careerFit: profileData.careerFit,
      },
    });
  }

  // Get profile by assessment ID
  async getProfile(assessmentId: string) {
    const profile = await prisma.profile.findUnique({
      where: { assessmentId },
    });

    if (!profile) return null;

    return {
      ...profile,
      strengths: JSON.parse(profile.strengths),
      careerSuggestions: JSON.parse(profile.careerSuggestions),
      developmentAreas: JSON.parse(profile.developmentAreas),
      personalityInsights: JSON.parse(profile.personalityInsights),
    };
  }

  // Get assessment with profile
  async getAssessmentWithProfile(assessmentId: string) {
    const assessment = await this.getAssessmentWithScores(assessmentId);
    if (!assessment) return null;

    const profile = await this.getProfile(assessmentId);
    
    return {
      assessment,
      profile,
    };
  }

  // Create user (for future use)
  async createUser(email?: string, name?: string) {
    return await prisma.user.create({
      data: { email, name },
    });
  }

  // Get user assessments
  async getUserAssessments(userId: string) {
    return await prisma.assessment.findMany({
      where: { userId },
      include: { profile: true },
      orderBy: { createdAt: 'desc' },
    });
  }

  // Update assessment profile status
  async markProfileGenerated(assessmentId: string) {
    return await prisma.assessment.update({
      where: { id: assessmentId },
      data: { updatedAt: new Date() },
    });
  }

  // Delete assessment and related profile
  async deleteAssessment(assessmentId: string) {
    return await prisma.assessment.delete({
      where: { id: assessmentId },
    });
  }

  // Get recent assessments (for analytics)
  async getRecentAssessments(limit: number = 10) {
    return await prisma.assessment.findMany({
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: { profile: true },
    });
  }

  // Health check for database
  async healthCheck() {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString() 
      };
    }
  }
}

// Export singleton instance
export const db = new DatabaseService();
